import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Question } from '../QuestionArea';
import './ExamCreator.css';
import { FiPlus, FiEdit, FiTrash2, FiArrowUp, FiArrowDown, FiSave, FiX, FiCheckCircle, FiPlay, FiClock, FiInfo, FiRefreshCw } from 'react-icons/fi';
import { getAudioDuration, calculateTotalListeningDuration, formatTimeMMSS, parseTimeMMSS } from '../../lib/utils';
import AudioTimelineRecorder from './AudioTimelineRecorder';
import './AudioTimelineRecorder.css';

// Khai báo kiểu cho window
declare global {
  interface Window {
    currentListeningTimeMMSS?: string;
    exactListeningDuration?: number;
    exactListeningTimeMMSS?: string;
  }
}

interface ExamCreatorProps {
  onSaveExam: (exam: any) => void;
  examId?: number;
}

// Question type for our form
interface QuestionFormData {
  id: number;
  display_index?: number; // Thứ tự hiển thị của câu hỏi
  type: 'reading' | 'listening';
  text: string;
  subText: string;
  hasImageOptions: boolean;
  correctAnswer: number; // ID của đáp án đúng
  audioUrl?: string;    // URL to audio file for listening questions
  start_at?: number;    // Thời điểm bắt đầu của câu hỏi (tính bằng giây) - Lưu trong database
  options: Array<{
    id: number;
    text: string;
    imageUrl: string;
  }>;
  // Trường chỉ dùng trên frontend, không lưu vào database
  audioDuration?: number; // Thời lượng của file âm thanh tính bằng giây (tính từ start_at, không lưu trong DB)
}

const ExamCreator: React.FC<ExamCreatorProps> = ({ onSaveExam, examId }) => {
  const [examTitle, setExamTitle] = useState('');
  const [examDescription, setExamDescription] = useState('');
  const [readingTimeMMSS, setReadingTimeMMSS] = useState('40:00');
  const [listeningTimeMMSS, setListeningTimeMMSS] = useState('30:00');
  // Lưu thời gian theo giây thay vì phút
  const [readingTimeSeconds, setReadingTimeSeconds] = useState(40 * 60);
  const [listeningTimeSeconds, setListeningTimeSeconds] = useState(30 * 60);
  // Giữ các state cũ để tương thích ngược
  const [readingTime, setReadingTime] = useState(40);
  const [listeningTime, setListeningTime] = useState(30);
  const [listeningAudioUrl, setListeningAudioUrl] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [questions, setQuestions] = useState<QuestionFormData[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState<QuestionFormData | null>(null);
  const [isEditingQuestion, setIsEditingQuestion] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isCheckingAudioDuration, setIsCheckingAudioDuration] = useState(false);
  const [listeningDurationSeconds, setListeningDurationSeconds] = useState(0);
  const [showAudioRecorder, setShowAudioRecorder] = useState(false);
  const [listeningQuestions, setListeningQuestions] = useState<Question[]>([]);
  const [existingStartTimes, setExistingStartTimes] = useState<number[]>([]);

  // Đồng bộ giữa các state thời gian
  useEffect(() => {
    // Đồng bộ từ readingTimeSeconds sang readingTimeMMSS
    setReadingTimeMMSS(formatTimeMMSS(readingTimeSeconds));
    // Đồng bộ với state cũ để tương thích ngược
    setReadingTime(Math.ceil(readingTimeSeconds / 60));
  }, [readingTimeSeconds]);

  useEffect(() => {
    // Đồng bộ từ listeningTimeSeconds sang listeningTimeMMSS
    // Chỉ cập nhật nếu listeningDurationSeconds không có giá trị
    // Để ưu tiên sử dụng thời lượng file chính xác
    if (listeningDurationSeconds <= 0) {
      setListeningTimeMMSS(formatTimeMMSS(listeningTimeSeconds));
    }
    // Đồng bộ với state cũ để tương thích ngược
    setListeningTime(Math.ceil(listeningTimeSeconds / 60));
  }, [listeningTimeSeconds, listeningDurationSeconds]);

  useEffect(() => {
    // Đồng bộ từ readingTimeMMSS sang readingTimeSeconds
    const seconds = parseTimeMMSS(readingTimeMMSS);
    if (seconds !== readingTimeSeconds) {
      console.log(`Updating readingTimeSeconds to ${seconds}s from readingTimeMMSS=${readingTimeMMSS}`);
      setReadingTimeSeconds(seconds);
    }
  }, [readingTimeMMSS]);

  useEffect(() => {
    // Đồng bộ từ listeningTimeMMSS sang listeningTimeSeconds
    const seconds = parseTimeMMSS(listeningTimeMMSS);

    // Chỉ cập nhật listeningTimeSeconds khi cần thiết
    if (seconds !== listeningTimeSeconds) {
      console.log(`Updating listeningTimeSeconds to ${seconds}s from listeningTimeMMSS=${listeningTimeMMSS}`);
      setListeningTimeSeconds(seconds);
    }

    // Cập nhật listeningDurationSeconds nếu có thay đổi từ người dùng
    // Vì đã disable input, nên phần này không còn cần thiết
    // Nhưng vẫn giữ lại để đảm bảo tương thích ngược
    if (seconds > 0 && seconds !== listeningDurationSeconds && window.exactListeningDuration === undefined) {
      console.log(`Updating listeningDurationSeconds to ${seconds}s from listeningTimeMMSS=${listeningTimeMMSS}`);
      setListeningDurationSeconds(seconds);
    }
  }, [listeningTimeMMSS]);

  // Handle listening audio URL change
  const handleListeningAudioUrlChange = (url: string) => {
    setListeningAudioUrl(url);
  };

  // Handle opening audio recorder
  const handleOpenAudioRecorder = () => {
    if (!listeningAudioUrl) {
      alert('Vui lòng nhập URL file âm thanh trước.');
      return;
    }

    const listeningQs = questions.filter(q => q.type === 'listening');
    if (listeningQs.length === 0) {
      alert('Không có câu hỏi listening nào trong bài thi.');
      return;
    }

    // Get existing start times from listening questions
    const startTimes = listeningQs
      .sort((a, b) => (a.display_index || 0) - (b.display_index || 0))
      .map(q => q.start_at !== undefined ? q.start_at : 0);

    console.log('Existing start times:', startTimes);

    setListeningQuestions(listeningQs);
    setExistingStartTimes(startTimes);
    setShowAudioRecorder(true);
  };

  const handleStartTimesRecorded = (startTimes: number[], markerCount?: number, audioDuration?: number) => {
    // startTimes đã bao gồm thời điểm 0 ở đầu danh sách (câu hỏi đầu tiên luôn bắt đầu từ 0s)
    // và các thời điểm chuyển tiếp giữa các câu hỏi

    // Đảm bảo tất cả các giá trị thời gian đều là số nguyên
    const roundedStartTimes = startTimes.map(time => Math.round(time));
    const roundedAudioDuration = audioDuration ? Math.round(audioDuration) : undefined;

    console.log('Received start times:', startTimes);
    console.log('Rounded start times:', roundedStartTimes);
    console.log('Received audio duration:', audioDuration);
    console.log('Rounded audio duration:', roundedAudioDuration);

    // Cập nhật thời lượng file nghe nếu có
    if (roundedAudioDuration && roundedAudioDuration > 0) {
      console.log(`Setting listeningDurationSeconds to ${roundedAudioDuration}s (was ${listeningDurationSeconds}s)`);
      setListeningDurationSeconds(roundedAudioDuration);

      // Cập nhật luôn giá trị Thời gian phần Listening theo định dạng mm:ss
      const newListeningTimeMMSS = formatTimeMMSS(roundedAudioDuration);
      console.log(`Setting listeningTimeMMSS to ${newListeningTimeMMSS} (was ${listeningTimeMMSS})`);

      // Lưu giá trị mới vào biến toàn cục để sử dụng sau này
      window.exactListeningDuration = roundedAudioDuration;
      window.exactListeningTimeMMSS = newListeningTimeMMSS;
      console.log(`Updated global variables: exactListeningDuration=${window.exactListeningDuration}, exactListeningTimeMMSS=${window.exactListeningTimeMMSS}`);

      // Cập nhật listeningTimeSeconds với giá trị chính xác
      console.log(`Setting listeningTimeSeconds to ${roundedAudioDuration}s (was ${listeningTimeSeconds}s)`);
      setListeningTimeSeconds(roundedAudioDuration);

      // Đảm bảo listeningTimeMMSS được cập nhật ngay lập tức
      // Sử dụng setTimeout để đảm bảo cập nhật sau khi render
      setListeningTimeMMSS(newListeningTimeMMSS);
      setTimeout(() => {
        if (listeningTimeMMSS !== newListeningTimeMMSS) {
          console.log(`Re-setting listeningTimeMMSS to ${newListeningTimeMMSS} (was ${listeningTimeMMSS})`);
          setListeningTimeMMSS(newListeningTimeMMSS);
        }
      }, 100);

      // Cập nhật cả giá trị phút để tương thích ngược
      const newListeningTimeMinutes = Math.ceil(roundedAudioDuration / 60);
      console.log(`Setting listeningTime to ${newListeningTimeMinutes} minutes (was ${listeningTime} minutes)`);
      setListeningTime(newListeningTimeMinutes);

      console.log(`Updated listening duration to ${roundedAudioDuration}s (${newListeningTimeMinutes} minutes)`);
    } else {
      console.warn(`No audioDuration provided or invalid value: ${audioDuration}`);
    }

    // Đảm bảo câu hỏi đầu tiên luôn có start_at = 0
    if (roundedStartTimes.length > 0 && roundedStartTimes[0] !== 0) {
      roundedStartTimes[0] = 0;
      console.log('Forced first question start time to 0s');
    }

    // Đảm bảo các thời điểm được sắp xếp theo thứ tự tăng dần
    roundedStartTimes.sort((a, b) => a - b);
    console.log('Sorted rounded start times:', roundedStartTimes);
    console.log('Marker count:', markerCount);
    console.log('Current listening questions:', listeningQuestions);

    // Calculate durations from start times (for display purposes only)
    const durations: number[] = [];
    for (let i = 0; i < roundedStartTimes.length - 1; i++) {
      // Đảm bảo start_at của câu hiện tại và câu tiếp theo được xử lý đúng
      const currentStartAt = roundedStartTimes[i];
      const nextStartAt = roundedStartTimes[i + 1];

      // Kiểm tra và đảm bảo các giá trị hợp lệ
      if (isNaN(currentStartAt) || isNaN(nextStartAt)) {
        console.error(`Invalid start times: roundedStartTimes[${i}]=${currentStartAt}, roundedStartTimes[${i+1}]=${nextStartAt}`);
        continue;
      }

      // Tính toán duration
      const duration = nextStartAt - currentStartAt;

      // Đảm bảo duration luôn dương và ít nhất 1 giây và là số nguyên
      const validDuration = Math.round(Math.max(duration, 1));
      durations.push(validDuration);

      // Log thông tin chi tiết hơn
      console.log(`Duration calculation: roundedStartTimes[${i}]=${currentStartAt}, roundedStartTimes[${i+1}]=${nextStartAt}, raw duration=${duration}, valid duration=${validDuration}`);
    }

    // Add duration for the last question (from last start time to end of audio)
    if (roundedStartTimes.length > 0) {
      const lastStartTime = roundedStartTimes[roundedStartTimes.length - 1];

      // Kiểm tra và đảm bảo giá trị hợp lệ
      if (isNaN(lastStartTime)) {
        console.error(`Invalid last start time: ${lastStartTime}`);
      } else {
        // Đảm bảo listeningDurationSeconds đã được cập nhật
        if (listeningDurationSeconds <= 0) {
          // Kiểm tra xem listeningTimeSeconds có giá trị không
          if (listeningTimeSeconds > 0) {
            console.log(`Using listeningTimeSeconds=${listeningTimeSeconds}s for last question duration`);
            const lastDuration = Math.round(Math.max(listeningTimeSeconds - lastStartTime, 5));
            durations.push(lastDuration);
            console.log(`Last duration calculation (from listeningTimeSeconds): lastStartTime=${lastStartTime}, listeningTimeSeconds=${listeningTimeSeconds}, lastDuration=${lastDuration}`);
          } else {
            console.warn('Both listeningDurationSeconds and listeningTimeSeconds are not set, using default value of 60 seconds');
            // Sử dụng giá trị mặc định 60 giây nếu chưa có giá trị
            const lastDuration = Math.round(Math.max(60 - lastStartTime, 1));
            durations.push(lastDuration);
            console.log(`Last duration calculation (with default duration): lastStartTime=${lastStartTime}, default duration=60s, lastDuration=${lastDuration}`);
          }
        } else {
          // Tính toán thời lượng cho câu cuối cùng = tổng thời lượng file - thời điểm bắt đầu của câu
          const lastDuration = Math.round(Math.max(listeningDurationSeconds - lastStartTime, 5));
          durations.push(lastDuration);
          console.log(`Last duration calculation: lastStartTime=${lastStartTime}, listeningDurationSeconds=${listeningDurationSeconds}, lastDuration=${lastDuration}`);
        }
      }
    }

    console.log('Calculated durations from start times (for display only):', durations);

    // Check if we need to create new questions
    // Số lượng câu hỏi = số lượng start times
    // startTimes đã bao gồm cả thời điểm 0 cho câu hỏi đầu tiên
    if (startTimes.length > listeningQuestions.length) {
      // Calculate how many new questions we need to create
      const newQuestionsCount = startTimes.length - listeningQuestions.length;
      console.log(`Creating ${newQuestionsCount} new listening questions`);

      // Get the highest ID and display_index from existing questions
      const highestId = Math.max(...questions.map(q => q.id), 0);
      const highestDisplayIndex = Math.max(...questions.map(q => q.display_index || 0), 0);

      // Create new questions
      const newQuestions: QuestionFormData[] = [];
      for (let i = 0; i < newQuestionsCount; i++) {
        const newId = highestId + i + 1;
        const newDisplayIndex = highestDisplayIndex + i + 1;
        const durationIndex = listeningQuestions.length + i;

        // Create a new question with default values
        newQuestions.push({
          id: newId,
          display_index: newDisplayIndex,
          type: 'listening',
          text: 'Câu mới',
          subText: '',
          hasImageOptions: false,
          correctAnswer: 1, // Default to first option
          start_at: i < roundedStartTimes.length ? roundedStartTimes[durationIndex] : 0, // Start time from markers
          audioDuration: i < durations.length ? durations[durationIndex] : 5, // For display only
          options: [
            { id: 1, text: 'Câu mới', imageUrl: '' },
            { id: 2, text: 'Câu mới', imageUrl: '' },
            { id: 3, text: 'Câu mới', imageUrl: '' },
            { id: 4, text: 'Câu mới', imageUrl: '' }
          ]
        });
      }

      // Update existing questions with their start times (and calculate durations for display)
      const updatedExistingQuestions = questions.map(q => {
        if (q.type === 'listening') {
          const index = listeningQuestions.findIndex(lq => lq.id === q.id);
          if (index !== -1 && index < roundedStartTimes.length) {
            // Đảm bảo câu hỏi đầu tiên luôn có start_at = 0
            const start_at = index === 0 ? 0 : roundedStartTimes[index];
            console.log(`Updating question ${q.id} with start time ${start_at}s (duration ${index < durations.length ? durations[index] : 'unknown'}s for display)`);
            return {
              ...q,
              start_at,
              audioDuration: index < durations.length ? durations[index] : 0 // For display only
            };
          }
        }
        return q;
      });

      // Combine existing and new questions
      const finalQuestions = [...updatedExistingQuestions, ...newQuestions];
      console.log('Updated questions with new durations:', finalQuestions);
      setQuestions(finalQuestions);

      // Sử dụng thời lượng file nghe thay vì tính tổng duration
      // Cập nhật listeningTimeMMSS với thời lượng chính xác đến từng giây
      const formattedTime = formatTimeMMSS(listeningDurationSeconds);
      console.log(`Setting listeningTimeMMSS to exact file duration: ${formattedTime} (${listeningDurationSeconds}s)`);

      // Cập nhật listeningTimeSeconds với giá trị chính xác
      console.log(`Setting listeningTimeSeconds to ${listeningDurationSeconds}s (was ${listeningTimeSeconds}s)`);
      setListeningTimeSeconds(listeningDurationSeconds);

      // Đảm bảo listeningTimeMMSS được cập nhật ngay lập tức
      setListeningTimeMMSS(formattedTime);
      // Lưu giá trị mới để sử dụng trong thông báo
      window.currentListeningTimeMMSS = formattedTime;
      window.exactListeningDuration = listeningDurationSeconds;
      window.exactListeningTimeMMSS = formattedTime;

      // Convert to minutes and round up for listeningTime (tương thích ngược)
      const totalListeningMinutes = Math.ceil(listeningDurationSeconds / 60);
      console.log(`Total listening time from file duration: ${listeningDurationSeconds}s (${totalListeningMinutes} minutes)`);
      // Update listening time
      setListeningTime(totalListeningMinutes);

      // Log giá trị trước khi hiển thị thông báo
      console.log(`Before alert - window.currentListeningTimeMMSS: ${window.currentListeningTimeMMSS}, formattedTime: ${formattedTime}`);

      // Lấy giá trị chính xác từ biến toàn cục
      const displayTime = window.exactListeningTimeMMSS || formattedTime;
      console.log(`Using displayTime=${displayTime} (exactListeningTimeMMSS=${window.exactListeningTimeMMSS}, formattedTime=${formattedTime})`);

      // Show confirmation message
      const message = `Đã tạo thêm ${newQuestionsCount} câu hỏi nghe mới và cập nhật thời điểm bắt đầu cho tất cả ${roundedStartTimes.length} câu hỏi.\n\nThời gian phần Listening đã được tự động cập nhật thành ${displayTime} dựa trên thời lượng file âm thanh.`;
      console.log(`Alert message: ${message}`);
      alert(message);
    } else {
      // Just update existing questions with their start times (and calculate durations for display)
      const updatedQuestions = questions.map(q => {
        if (q.type === 'listening') {
          const index = listeningQuestions.findIndex(lq => lq.id === q.id);
          if (index !== -1 && index < roundedStartTimes.length) {
            // Đảm bảo câu hỏi đầu tiên luôn có start_at = 0
            const start_at = index === 0 ? 0 : roundedStartTimes[index];
            console.log(`Updating question ${q.id} with start time ${start_at}s (duration ${index < durations.length ? durations[index] : 'unknown'}s for display)`);
            return {
              ...q,
              start_at,
              audioDuration: index < durations.length ? durations[index] : 0 // For display only
            };
          }
        }
        return q;
      });

      console.log('Updated questions with new durations:', updatedQuestions);
      setQuestions(updatedQuestions);

      // Sử dụng thời lượng file nghe thay vì tính tổng duration
      // Cập nhật listeningTimeMMSS với thời lượng chính xác đến từng giây
      const formattedTime = formatTimeMMSS(listeningDurationSeconds);
      console.log(`Setting listeningTimeMMSS to exact file duration: ${formattedTime} (${listeningDurationSeconds}s)`);
      // Đảm bảo listeningTimeMMSS được cập nhật ngay lập tức
      setListeningTimeMMSS(formattedTime);
      // Lưu giá trị mới để sử dụng trong thông báo
      window.currentListeningTimeMMSS = formattedTime;

      // Convert to minutes and round up for listeningTime (tương thích ngược)
      const totalListeningMinutes = Math.ceil(listeningDurationSeconds / 60);
      console.log(`Total listening time from file duration: ${listeningDurationSeconds}s (${totalListeningMinutes} minutes)`);
      // Update listening time
      setListeningTime(totalListeningMinutes);

      // Log giá trị trước khi hiển thị thông báo
      console.log(`Before alert - window.currentListeningTimeMMSS: ${window.currentListeningTimeMMSS}, formattedTime: ${formattedTime}`);

      // Lấy giá trị chính xác từ biến toàn cục
      const displayTime = window.exactListeningTimeMMSS || formattedTime;
      console.log(`Using displayTime=${displayTime} (exactListeningTimeMMSS=${window.exactListeningTimeMMSS}, formattedTime=${formattedTime})`);

      // Show confirmation message
      const message = `Đã cập nhật thời điểm bắt đầu cho ${Math.min(roundedStartTimes.length, listeningQuestions.length)} câu hỏi nghe.\n\nThời gian phần Listening đã được tự động cập nhật thành ${displayTime} dựa trên thời lượng file âm thanh.`;
      console.log(`Alert message: ${message}`);
      alert(message);
    }

    // We don't need to close the recorder here because it will close itself
    // after showing the success message in AudioTimelineRecorder.handleSave
  };

  // Hàm kiểm tra duration khi bấm nút
  const handleCheckAudioDuration = async () => {
    if (!listeningAudioUrl) return;
    setIsCheckingAudioDuration(true);
    setError(null);
    try {
      // Sử dụng hàm getAudioDuration đã được cập nhật để sử dụng URL trực tiếp
      const durationSec = await getAudioDuration(listeningAudioUrl);
      if (durationSec > 0) {
        console.log(`Got audio duration: ${durationSec}s`);
        setListeningDurationSeconds(durationSec);

        // Cập nhật trực tiếp listeningTimeMMSS từ thời lượng file chính xác đến từng giây
        const formattedTime = formatTimeMMSS(durationSec);
        console.log(`Setting listeningTimeMMSS to exact file duration: ${formattedTime}`);

        // Lưu giá trị mới vào biến toàn cục để sử dụng sau này
        window.exactListeningDuration = durationSec;
        window.exactListeningTimeMMSS = formattedTime;
        window.currentListeningTimeMMSS = formattedTime;
        console.log(`Updated global variables: exactListeningDuration=${window.exactListeningDuration}, exactListeningTimeMMSS=${window.exactListeningTimeMMSS}`);

        // Cập nhật listeningTimeSeconds với giá trị chính xác
        console.log(`Setting listeningTimeSeconds to ${durationSec}s (was ${listeningTimeSeconds}s)`);
        setListeningTimeSeconds(durationSec);

        // Đảm bảo listeningTimeMMSS được cập nhật ngay lập tức
        setListeningTimeMMSS(formattedTime);
        setTimeout(() => {
          if (listeningTimeMMSS !== formattedTime) {
            console.log(`Re-setting listeningTimeMMSS to ${formattedTime} (was ${listeningTimeMMSS})`);
            setListeningTimeMMSS(formattedTime);
          }
        }, 100);

        // Cập nhật cả listeningTime để tương thích ngược
        const minutes = Math.ceil(durationSec / 60);
        setListeningTime(minutes);
      } else {
        setError('Không thể lấy thời lượng file âm thanh. Có thể file chưa được chia sẻ công khai hoặc URL không đúng định dạng Google Drive.');
      }
    } catch (err: any) {
      if (err?.message?.includes('Timeout')) {
        setError('Timeout khi tải file âm thanh. Vui lòng kiểm tra lại kết nối hoặc thử lại sau.');
      } else if (err?.message?.includes('ID file')) {
        setError('URL không đúng định dạng Google Drive.');
      } else {
        setError('Không thể tải thông tin thời lượng file âm thanh. Vui lòng kiểm tra lại URL hoặc quyền chia sẻ file.');
      }
    } finally {
      setIsCheckingAudioDuration(false);
    }
  };

  // Fetch exam data if examId is provided
  useEffect(() => {
    if (examId) {
      const fetchExam = async () => {
        try {
          setLoading(true);
          const response = await fetch(`/api/exams/${examId}`);
          if (!response.ok) {
            throw new Error('Failed to fetch exam');
          }
          const examData = await response.json();
          // Debug log
          console.log('[DEBUG] examData:', examData);
          console.log('[DEBUG] Loaded examData.listeningAudioUrl:', examData.listeningAudioUrl);
          // Set form values
          setExamTitle(examData.title);
          setExamDescription(examData.description || '');

          // Cập nhật thời gian theo giây và định dạng mm:ss
          // Kiểm tra xem DB đã lưu theo giây hay phút
          let readingTimeSec, listeningTimeSec;

          // Đảm bảo readingTime luôn được xử lý theo giây
          // Cho phép tương thích ngược với dữ liệu cũ (có thể được lưu theo phút)
          // Đảm bảo giá trị là số
          const readingTimeValue = Number(examData.readingTime);
          if (readingTimeValue < 100) {
            // Nếu giá trị nhỏ hơn 100, giả định đây là phút và chuyển đổi sang giây
            readingTimeSec = readingTimeValue * 60;
            console.log(`Converting readingTime from minutes to seconds: ${readingTimeValue} minutes = ${readingTimeSec} seconds`);
          } else {
            // Nếu giá trị lớn hơn hoặc bằng 100, giả định đã lưu theo giây
            readingTimeSec = readingTimeValue;
            console.log(`Using readingTime in seconds: ${readingTimeSec} seconds`);
          }

          // Đảm bảo listeningTime luôn được xử lý theo giây
          // Cho phép tương thích ngược với dữ liệu cũ (có thể được lưu theo phút)
          // Đảm bảo giá trị là số
          const listeningTimeValue = Number(examData.listeningTime);
          if (listeningTimeValue < 100) {
            // Nếu giá trị nhỏ hơn 100, giả định đây là phút và chuyển đổi sang giây
            listeningTimeSec = listeningTimeValue * 60;
            console.log(`Converting listeningTime from minutes to seconds: ${listeningTimeValue} minutes = ${listeningTimeSec} seconds`);
          } else {
            // Nếu giá trị lớn hơn hoặc bằng 100, giả định đã lưu theo giây
            listeningTimeSec = listeningTimeValue;
            console.log(`Using listeningTime in seconds: ${listeningTimeSec} seconds`);
          }

          // Ưu tiên sử dụng listeningAudioDuration nếu có (chính xác đến từng giây)
          if (examData.listeningAudioDuration && examData.listeningAudioDuration > 0) {
            listeningTimeSec = examData.listeningAudioDuration;
            console.log(`Using listeningAudioDuration for listeningTimeSeconds: ${listeningTimeSec} seconds`);

            // Lưu giá trị vào biến toàn cục
            window.exactListeningDuration = examData.listeningAudioDuration;
            window.exactListeningTimeMMSS = formatTimeMMSS(examData.listeningAudioDuration);
            console.log(`Updated global variables from database: exactListeningDuration=${window.exactListeningDuration}, exactListeningTimeMMSS=${window.exactListeningTimeMMSS}`);
          }

          // Cập nhật các state mới
          setReadingTimeSeconds(readingTimeSec);
          setListeningTimeSeconds(listeningTimeSec);
          setReadingTimeMMSS(formatTimeMMSS(readingTimeSec));
          setListeningTimeMMSS(formatTimeMMSS(listeningTimeSec));

          // Cập nhật các giá trị cũ để tương thích ngược
          setReadingTime(Math.ceil(readingTimeSec / 60));
          setListeningTime(Math.ceil(listeningTimeSec / 60));

          setListeningAudioUrl(examData.listeningAudioUrl || '');
          setIsActive(examData.isActive === undefined ? true : examData.isActive);
          setListeningDurationSeconds(examData.listeningAudioDuration || listeningTimeSec);

          // Parse questions from JSON and format them for the form
          if (examData.questions) {
            const parsedQuestions = typeof examData.questions === 'string'
              ? JSON.parse(examData.questions)
              : examData.questions;

            // Convert to QuestionFormData format
            const formattedQuestions = parsedQuestions.map((q: any) => ({
              id: q.id,
              type: q.type,
              text: q.text,
              subText: q.subText || '',
              hasImageOptions: q.hasImageOptions || false,
              correctAnswer: q.correctAnswer || 1, // Mặc định đáp án 1 là đúng
              audioUrl: q.audioUrl || '',  // Fix: Lưu URL audio
              start_at: q.start_at || 0, // Thời điểm bắt đầu của câu hỏi
              audioDuration: 0, // Thời lượng âm thanh (sẽ được tính từ start_at)
              options: q.options.map((opt: any) => ({
                id: opt.id,
                text: opt.text || '',
                imageUrl: opt.imageUrl || '',
              })),
            }));

            // Thêm tất cả câu hỏi vào danh sách, không đặt câu hỏi hiện tại
            if (formattedQuestions.length > 0) {
              // Tính toán thời lượng cho các câu hỏi nghe dựa trên start_at
              const listeningQuestions = formattedQuestions
                .filter(q => q.type === 'listening')
                .sort((a, b) => (a.start_at || 0) - (b.start_at || 0));

              // Log thông tin các câu hỏi nghe
              console.log('Listening questions before calculating durations:', JSON.stringify(listeningQuestions));

              // Tính toán thời lượng cho mỗi câu hỏi nghe
              if (listeningQuestions.length > 0) {
                for (let i = 0; i < listeningQuestions.length; i++) {
                  const currentQuestion = listeningQuestions[i];

                  // Nếu là câu hỏi cuối cùng
                  if (i === listeningQuestions.length - 1) {
                    // Đảm bảo start_at của câu hiện tại được xử lý đúng và là số nguyên
                    const currentStartAt = currentQuestion.start_at !== undefined ? Math.round(currentQuestion.start_at) : 0;

                    // Tính toán thời lượng cho câu cuối cùng = Thời gian phần Listening - thời điểm bắt đầu của câu
                    // Sử dụng listeningTimeSec đã được tính toán ở trên

                    // Ưu tiên sử dụng listeningAudioDuration nếu có (chính xác đến từng giây)
                    if (examData.listeningAudioDuration && examData.listeningAudioDuration > 0) {
                      currentQuestion.audioDuration = Math.max(examData.listeningAudioDuration - currentStartAt, 30);
                      console.log(`Last question duration from listeningAudioDuration: ${currentQuestion.audioDuration}s (total=${examData.listeningAudioDuration}s, start_at=${currentStartAt}s)`);
                    } else if (listeningTimeSec > 0) {
                      // Sử dụng listeningTimeSec đã được tính toán ở trên
                      // Đảm bảo thời lượng là số nguyên
                      currentQuestion.audioDuration = Math.round(Math.max(listeningTimeSec - currentStartAt, 30));
                      console.log(`Last question duration from listeningTimeSec: ${currentQuestion.audioDuration}s (listeningTimeSec=${listeningTimeSec}s, start_at=${currentStartAt}s)`);
                    } else {
                      // Nếu không có cả hai thông tin, sử dụng giá trị mặc định 60 giây cho câu cuối cùng
                      console.warn('Both listeningTime and listeningAudioDuration are missing, using default duration of 60s for last question');
                      currentQuestion.audioDuration = 60;
                    }

                    console.log(`Last question duration: ${currentQuestion.audioDuration}s (total=${examData.listeningAudioDuration || 'unknown'}s, start_at=${currentStartAt}s)`);
                  } else {
                    // Nếu không phải câu hỏi cuối cùng
                    const nextQuestion = listeningQuestions[i + 1];
                    // Đảm bảo start_at của câu hiện tại và câu tiếp theo được xử lý đúng và là số nguyên
                    const currentStartAt = currentQuestion.start_at !== undefined ? Math.round(currentQuestion.start_at) : 0;
                    const nextStartAt = nextQuestion.start_at !== undefined ? Math.round(nextQuestion.start_at) : Math.round(listeningTimeSec);
                    // Tính toán thời lượng = thời điểm bắt đầu của câu tiếp theo - thời điểm bắt đầu của câu hiện tại
                    // Đảm bảo thời lượng ít nhất là 1 giây và là số nguyên
                    currentQuestion.audioDuration = Math.round(Math.max(nextStartAt - currentStartAt, 1));
                    console.log(`Question ${i+1} duration: ${currentQuestion.audioDuration}s (current start_at=${currentStartAt}s, next start_at=${nextStartAt}s)`);
                  }
                }
              }

              // Log thông tin các câu hỏi nghe sau khi tính toán thời lượng
              console.log('Listening questions after calculating durations:',
                listeningQuestions.map(q => ({
                  id: q.id,
                  start_at: q.start_at,
                  audioDuration: q.audioDuration
                })));

              setQuestions(formattedQuestions);
              setIsEditingQuestion(false);
              setCurrentQuestion(null);
            }
          }

          setError(null);
        } catch (err) {
          console.error('Error fetching exam:', err);
          setError('Không thể tải thông tin đề thi. Vui lòng thử lại sau.');
        } finally {
          setLoading(false);
        }
      };

      fetchExam();
    }
  }, [examId]);

  // Khởi tạo câu hỏi mới
  const createNewQuestion = () => {
    const nextIndex = questions.length + 1;
    setCurrentQuestion({
      id: questions.length > 0 ? Math.max(...questions.map(q => q.id)) + 1 : 1,
      display_index: nextIndex, // Thứ tự hiển thị mặc định là số tiếp theo
      type: 'reading',
      text: '',
      subText: '',
      hasImageOptions: false,
      correctAnswer: 1, // Mặc định đáp án 1 là đúng
      audioUrl: '', // URL âm thanh trống
      start_at: 0, // Thời điểm bắt đầu mặc định là 0
      audioDuration: 0, // Thời lượng ban đầu là 0 (chỉ hiển thị, không lưu)
      options: [
        { id: 1, text: '', imageUrl: '' },
        { id: 2, text: '', imageUrl: '' },
        { id: 3, text: '', imageUrl: '' },
        { id: 4, text: '', imageUrl: '' },
      ],
    });
    setIsEditingQuestion(true);
  };

  const handleQuestionChange = (field: string, value: any) => {
    if (!currentQuestion) return;

    // Ngăn chặn chỉnh sửa ID câu hỏi
    if (field === 'id') {
      console.log('ID câu hỏi không thể thay đổi');
      return;
    }

    setCurrentQuestion((prev) => ({
      ...prev!,
      [field]: value,
    }));
  };

  const handleOptionChange = (optionId: number, field: string, value: string) => {
    if (!currentQuestion) return;

    setCurrentQuestion((prev) => {
      const updatedOptions = prev!.options.map((option) =>
        option.id === optionId ? { ...option, [field]: value } : option
      );
      return { ...prev!, options: updatedOptions };
    });
  };

  const removeQuestion = (id: number) => {
    const newQuestions = questions.filter(q => q.id !== id);
    // Reindex remaining questions
    const reindexedQuestions = newQuestions.map((q, index) => ({
      ...q,
      display_index: index + 1
    }));
    setQuestions(reindexedQuestions);
  };

  const editQuestion = (id: number) => {
    const questionToEdit = questions.find((q) => q.id === id);
    if (questionToEdit) {
      // Tạo bản sao sâu của câu hỏi để tránh tham chiếu trực tiếp
      setCurrentQuestion(JSON.parse(JSON.stringify(questionToEdit)));
      setIsEditingQuestion(true);
      // Không xóa câu hỏi khỏi danh sách cho đến khi người dùng lưu thay đổi
    }
  };

  const saveQuestion = () => {
    if (!currentQuestion) return;

    if (!currentQuestion.text) {
      alert('Vui lòng nhập nội dung câu hỏi');
      return;
    }
    // Validate type
    if (!['reading', 'listening'].includes(currentQuestion.type)) {
      alert('Loại câu hỏi phải là reading hoặc listening.');
      return;
    }
    // Validate audioDuration cho câu hỏi listening
    if (currentQuestion.type === 'listening' && (!currentQuestion.audioDuration || currentQuestion.audioDuration <= 0)) {
      alert('Vui lòng nhập thời lượng cho câu hỏi listening (lớn hơn 0 giây).');
      return;
    }
    // Kiểm tra các đáp án
    if (currentQuestion.hasImageOptions) {
      const emptyImages = currentQuestion.options.filter(opt => !opt.imageUrl);
      if (emptyImages.length > 0) {
        alert('Vui lòng thêm URL hình ảnh cho tất cả các đáp án');
        return;
      }
    } else {
      const emptyTexts = currentQuestion.options.filter(opt => !opt.text);
      if (emptyTexts.length > 0) {
        alert('Vui lòng nhập nội dung cho tất cả các đáp án');
        return;
      }
    }
    // Kiểm tra display_index, nếu chưa có thì tạo mới
    if (!currentQuestion.display_index) {
      currentQuestion.display_index = questions.length + 1;
    }
    // Kiểm tra ID câu hỏi có bị trùng không
    const existingQuestionIndex = questions.findIndex(q => q.id === currentQuestion.id);
    if (existingQuestionIndex !== -1) {
      if (!window.confirm(`Câu hỏi với ID ${currentQuestion.id} đã tồn tại. Bạn có muốn thay thế không?`)) {
        return;
      }
      // Thay thế câu hỏi hiện có
      const updatedQuestions = [...questions];
      updatedQuestions[existingQuestionIndex] = {...currentQuestion};
      setQuestions(updatedQuestions);
    } else {
      // Thêm câu hỏi mới vào danh sách
      setQuestions(prev => [...prev, {...currentQuestion}]);
    }
    // Reset form câu hỏi
    setCurrentQuestion(null);
    setIsEditingQuestion(false);
  };

  const moveQuestion = (index: number, direction: 'up' | 'down') => {
    const newQuestions = [...questions];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    // Kiểm tra xem có đang cố gắng di chuyển qua ranh giới giữa phần đọc và phần nghe không
    const currentQuestion = newQuestions[index];
    const targetQuestion = newQuestions[newIndex];

    if (currentQuestion.type !== targetQuestion.type) {
      // Nếu đang cố gắng di chuyển qua ranh giới, hiển thị thông báo và không thực hiện thay đổi
      alert('Không thể di chuyển câu hỏi qua ranh giới giữa phần Reading và Listening!');
      return;
    }

    // Xử lý đặc biệt cho câu hỏi nghe
    if (currentQuestion.type === 'listening' && targetQuestion.type === 'listening') {
      console.log('Di chuyển câu hỏi nghe: chỉ đổi nội dung, giữ nguyên thời điểm bắt đầu');

      // Lưu thời điểm bắt đầu của các câu hỏi
      const currentStartAt = currentQuestion.start_at;
      const targetStartAt = targetQuestion.start_at;
      const currentAudioDuration = currentQuestion.audioDuration;
      const targetAudioDuration = targetQuestion.audioDuration;

      // Đổi nội dung câu hỏi nhưng giữ nguyên thời điểm bắt đầu
      const tempQuestion = { ...currentQuestion };

      // Cập nhật câu hỏi hiện tại với nội dung của câu hỏi đích
      newQuestions[index] = {
        ...targetQuestion,
        start_at: currentStartAt,
        audioDuration: currentAudioDuration,
        id: targetQuestion.id,  // Giữ nguyên ID
        display_index: targetQuestion.display_index  // Giữ nguyên display_index
      };

      // Cập nhật câu hỏi đích với nội dung của câu hỏi hiện tại
      newQuestions[newIndex] = {
        ...tempQuestion,
        start_at: targetStartAt,
        audioDuration: targetAudioDuration,
        id: tempQuestion.id,  // Giữ nguyên ID
        display_index: tempQuestion.display_index  // Giữ nguyên display_index
      };
    } else {
      // Đối với các câu hỏi đọc, hoặc trường hợp khác, thực hiện hoán đổi bình thường
      [newQuestions[index], newQuestions[newIndex]] = [newQuestions[newIndex], newQuestions[index]];
    }

    // Update display_index for all questions
    const updatedQuestions = newQuestions.map((q, idx) => ({
      ...q,
      display_index: idx + 1
    }));

    setQuestions(updatedQuestions);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Kiểm tra nếu đang chỉnh sửa câu hỏi, nhắc người dùng lưu trước
    if (isEditingQuestion && currentQuestion) {
      if (!window.confirm('Bạn vẫn đang chỉnh sửa một câu hỏi. Bạn muốn tiếp tục lưu đề thi và bỏ qua thay đổi cho câu hỏi này?')) {
        return;
      }
    }
    // Validate all questions before saving
    for (const q of questions) {
      if (!q.text) {
        alert(`Câu hỏi số ${q.display_index || q.id} thiếu nội dung.`);
        return;
      }
      if (!['reading', 'listening'].includes(q.type)) {
        alert(`Câu hỏi số ${q.display_index || q.id} có loại không hợp lệ.`);
        return;
      }
      if (q.hasImageOptions) {
        const emptyImages = q.options.filter(opt => !opt.imageUrl);
        if (emptyImages.length > 0) {
          alert(`Câu hỏi số ${q.display_index || q.id} thiếu hình ảnh cho đáp án.`);
          return;
        }
      } else {
        const emptyTexts = q.options.filter(opt => !opt.text);
        if (emptyTexts.length > 0) {
          alert(`Câu hỏi số ${q.display_index || q.id} thiếu nội dung đáp án.`);
          return;
        }
      }
    }
    // Validate listeningAudioUrl at exam level
    if (questions.some(q => q.type === 'listening') && !listeningAudioUrl) {
      alert('Bạn phải nhập URL file nghe cho phần Listening.');
      return;
    }
    // Ensure all questions have proper display_index
    const orderedQuestions = questions.map((q, index) => ({
      ...q,
      display_index: index + 1
    }));
    // Calculate total listening time in seconds from questions
    const listeningQuestions = questions.filter(q => q.type === 'listening');

    // Sort listening questions by start_at
    const sortedListeningQuestions = [...listeningQuestions].sort((a, b) => (a.start_at || 0) - (b.start_at || 0));

    // Log thông tin chi tiết về các câu hỏi nghe
    console.log('Sorted listening questions:', sortedListeningQuestions.map(q => ({ id: q.id, start_at: q.start_at })));

    // Lấy thời gian từ định dạng mm:ss
    const readingTimeSeconds = parseTimeMMSS(readingTimeMMSS);
    const listeningTimeSeconds = parseTimeMMSS(listeningTimeMMSS);

    console.log(`Submit: readingTimeMMSS=${readingTimeMMSS} (${readingTimeSeconds}s), listeningTimeMMSS=${listeningTimeMMSS} (${listeningTimeSeconds}s)`);

    // Đảm bảo các giá trị thời gian luôn hợp lệ
    if (readingTimeSeconds <= 0) {
      console.warn('readingTimeSeconds is invalid, using default value of 40 minutes');
      setReadingTimeMMSS('40:00');
    }

    // Ưu tiên sử dụng thời lượng file chính xác nếu có
    // Ưu tiên sử dụng giá trị từ biến toàn cục nếu có
    let finalListeningTimeSeconds = window.exactListeningDuration || (listeningDurationSeconds > 0 ? listeningDurationSeconds : listeningTimeSeconds);

    // Đảm bảo finalListeningTimeSeconds luôn có giá trị hợp lệ
    if (finalListeningTimeSeconds <= 0) {
      console.warn('Both listeningTimeSeconds and listeningDurationSeconds are not set, using default value of 30 minutes');
      finalListeningTimeSeconds = 30 * 60; // Giá trị mặc định 30 phút
      setListeningTimeMMSS('30:00');
    } else {
      // Cập nhật listeningTimeMMSS từ finalListeningTimeSeconds
      const formattedTime = formatTimeMMSS(finalListeningTimeSeconds);
      if (listeningTimeMMSS !== formattedTime) {
        console.log(`Updating listeningTimeMMSS to ${formattedTime} from finalListeningTimeSeconds=${finalListeningTimeSeconds}`);
        setListeningTimeMMSS(formattedTime);
      }
    }

    console.log(`Final listening time seconds: ${finalListeningTimeSeconds}s`);

    // Tính toán thời gian theo phút cho tương thích ngược với trường duration
    const finalReadingTime = Math.ceil(readingTimeSeconds / 60);
    const finalListeningTime = Math.ceil(finalListeningTimeSeconds / 60);

    // Log thông tin chi tiết
    console.log(`Final reading time: ${formatTimeMMSS(readingTimeSeconds)} (${readingTimeSeconds}s = ${finalReadingTime} minutes)`);
    console.log(`Final listening time: ${formatTimeMMSS(finalListeningTimeSeconds)} (${finalListeningTimeSeconds}s = ${finalListeningTime} minutes)`);

    // Calculate duration based on reading and listening time in minutes for backward compatibility
    // This is used for the total exam duration in minutes
    const calculatedDuration = finalReadingTime + finalListeningTime;
    console.log(`Calculated total exam duration: ${calculatedDuration} minutes (${finalReadingTime} + ${finalListeningTime})`);
    // Log thông tin trước khi tạo exam object
    console.log(`Before creating exam object: window.exactListeningDuration=${window.exactListeningDuration}, listeningDurationSeconds=${listeningDurationSeconds}, finalListeningTimeSeconds=${finalListeningTimeSeconds}`);
    console.log(`listeningTimeMMSS=${listeningTimeMMSS}, window.exactListeningTimeMMSS=${window.exactListeningTimeMMSS}`);

    // Create the exam object
    const exam = {
      title: examTitle,
      description: examDescription,
      // Lưu thời gian theo giây - đảm bảo luôn là số
      readingTime: Number(readingTimeSeconds),
      listeningTime: Number(finalListeningTimeSeconds),
      // Duration vẫn lưu theo phút cho tương thích ngược
      duration: Number(calculatedDuration),
      isActive,
      listeningAudioUrl,
      // Đảm bảo listeningAudioDuration luôn có giá trị hợp lệ và là số
      // Ưu tiên sử dụng giá trị chính xác từ biến toàn cục
      listeningAudioDuration: Number(window.exactListeningDuration || (listeningDurationSeconds > 0 ? listeningDurationSeconds : finalListeningTimeSeconds)),
      questions: orderedQuestions.map(q => {
        if (q.hasImageOptions) {
          return {
            id: q.id,
            display_index: q.display_index,
            type: q.type,
            text: q.text,
            subText: q.subText,
            hasImageOptions: true,
            correctAnswer: q.correctAnswer,
            start_at: q.start_at,
            options: q.options.map(opt => ({
              id: opt.id,
              imageUrl: opt.imageUrl
            }))
          };
        } else {
          return {
            id: q.id,
            display_index: q.display_index,
            type: q.type,
            text: q.text,
            subText: q.subText,
            correctAnswer: q.correctAnswer,
            start_at: q.start_at,
            options: q.options.map(opt => ({
              id: opt.id,
              text: opt.text
            }))
          };
        }
      })
    };

    try {
      // Debug log to see what we're sending to the server
      console.log('Sending exam to server:', {
        ...exam,
        readingTime: `${exam.readingTime} seconds (${Math.ceil(exam.readingTime / 60)} minutes)`,
        listeningTime: `${exam.listeningTime} seconds (${Math.ceil(exam.listeningTime / 60)} minutes)`,
      });

      // If editing an existing exam
      if (examId) {
        const response = await fetch(`/api/exams/${examId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(exam),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Server error response:', {
            status: response.status,
            statusText: response.statusText,
            body: errorText
          });
          throw new Error(`Failed to update exam: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const updatedExam = await response.json();
        console.log('Exam saved:', updatedExam);
        onSaveExam(updatedExam);
      }
      // Creating a new exam
      else {
        const response = await fetch('/api/exams', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(exam),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Server error response:', {
            status: response.status,
            statusText: response.statusText,
            body: errorText
          });
          throw new Error(`Failed to create exam: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const newExam = await response.json();
        console.log('Exam saved:', newExam);
        onSaveExam(newExam);
      }
    } catch (err) {
      console.error('Error saving exam:', err);
      // Show more detailed error message
      if (err instanceof Error) {
        setError(`Có lỗi xảy ra khi lưu đề thi: ${err.message}`);
      } else {
        setError('Có lỗi xảy ra khi lưu đề thi. Vui lòng thử lại sau.');
      }
    }
  };

  // Helper to format seconds to MM:SS
  const formatSecondsToMMSS = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="exam-list-loading">
        <div className="spinner"></div>
        <p>Đang tải thông tin đề thi...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="exam-list-error">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Thử lại</button>
      </div>
    );
  }

  return (
    <div className="admin-creator">
      <h2>{examId ? 'Chỉnh sửa đề thi' : 'Tạo đề thi EPS-TOPIK mới'}</h2>

      <form onSubmit={handleSubmit}>
        <div className="creator-section">
          <h3>Thông tin đề thi</h3>
          <div className="form-group">
            <label>Tiêu đề</label>
            <input
              type="text"
              value={examTitle}
              onChange={(e) => setExamTitle(e.target.value)}
              required
              placeholder="Nhập tiêu đề đề thi"
            />
          </div>
          <div className="form-group">
            <label>Mô tả</label>
            <textarea
              value={examDescription}
              onChange={(e) => setExamDescription(e.target.value)}
              placeholder="Nhập mô tả đề thi"
            />
          </div>
          <div className="form-group">
            <label>URL file nghe</label>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <input
                type="text"
                value={listeningAudioUrl}
                onChange={(e) => handleListeningAudioUrlChange(e.target.value)}
                placeholder="Nhập URL file âm thanh cho phần nghe"
                style={{ flex: 1 }}
              />
              <button
                type="button"
                className="icon-button info"
                onClick={handleCheckAudioDuration}
                disabled={!listeningAudioUrl || isCheckingAudioDuration}
                title="Kiểm tra thời lượng file audio"
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: 6 }}
              >
                {isCheckingAudioDuration ? (
                  <span className="spinner-audio" />
                ) : (
                  <FiClock size={18} />
                )}
              </button>
              <button
                type="button"
                className="icon-button"
                onClick={handleOpenAudioRecorder}
                disabled={!listeningAudioUrl}
                title="Ghi nhận thời lượng cho từng câu hỏi"
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: 6 }}
              >
                <FiPlay size={18} />
              </button>
            </div>
            {listeningDurationSeconds > 0 && (
              <div className="audio-duration-status">
                Thời lượng file nghe: {formatSecondsToMMSS(listeningDurationSeconds)}
              </div>
            )}
            {error && (
              <div style={{ color: 'red', marginTop: 4 }}>{error}</div>
            )}
          </div>
          <div className="form-group">
            <label>Thời gian phần Reading (mm:ss)</label>
            <input
              type="text"
              value={readingTimeMMSS}
              onChange={(e) => {
                setReadingTimeMMSS(e.target.value);
                // Cập nhật readingTimeSeconds khi người dùng nhập
                const seconds = parseTimeMMSS(e.target.value);
                if (seconds > 0) {
                  setReadingTimeSeconds(seconds);
                  console.log(`Updated readingTimeSeconds to ${seconds}s from user input`);
                }
              }}
              pattern="[0-9]{2}:[0-9]{2}"
              placeholder="mm:ss"
              required
            />
          </div>
          <div className="form-group">
            <label>Thời gian phần Listening (mm:ss)</label>
            <div className="input-with-hint">
              <input
                type="text"
                value={listeningTimeMMSS}
                disabled
                className="disabled-input"
                placeholder="mm:ss"
              />
              <div className="form-hint">
                <FiInfo size={14} style={{ marginRight: '4px' }} />
                Thời gian phần Listening được tính tự động từ thời lượng file âm thanh khi bạn lưu thời lượng câu hỏi. Sử dụng nút "Ghi nhận thời lượng cho từng câu hỏi" để cập nhật.
              </div>
            </div>
          </div>
          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
              />
              Đề thi đang hoạt động
            </label>
          </div>
        </div>

        {/* Phần quản lý danh sách câu hỏi */}
        <div className="creator-section">
          <div className="section-header">
            <h3>
              Danh sách câu hỏi ({questions.length})
            </h3>
            <button
              type="button"
              className="button-with-text primary"
              onClick={createNewQuestion}
            >
              <FiPlus size={18} /> Thêm câu hỏi mới
            </button>
          </div>

          {questions.length === 0 ? (
            <div className="empty-questions">
              <p>Chưa có câu hỏi nào. Nhấn "Thêm câu hỏi mới" để bắt đầu tạo câu hỏi.</p>
            </div>
          ) : (
            <div className="questions-table-container">
              <table className="questions-table">
                <thead>
                  <tr>
                    <th>STT</th>
                    <th>Loại</th>
                    <th>Câu hỏi</th>
                    <th>Đáp án</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {questions.map((q, index) => {
                    return (
                      <tr key={q.id}>
                        <td>{index + 1}</td>
                        <td>
                          <span className={`question-type ${q.type}`}>
                            {q.type === 'reading' ? 'Reading' : 'Listening'}
                          </span>
                        </td>
                        <td className="question-text-cell">
                          <div className="question-text">{q.text}</div>
                          {q.subText && (
                            <div className="question-subtext">{q.subText}</div>
                          )}
                        </td>
                        <td className="options-count">
                          {q.options.length} đáp án
                          {q.hasImageOptions && <span className="image-icon"> (Hình ảnh)</span>}
                          <div>Đáp án đúng: {q.correctAnswer}</div>
                          {q.type === 'listening' && (
                            <div className="audio-info">
                              <span className="audio-icon">🎵</span>
                              <div className="audio-details">
                                <span className="audio-start-time">
                                  Bắt đầu: {q.start_at !== undefined ? q.start_at : 0}s
                                </span>
                                {/* Tính thời lượng từ start_at của câu hiện tại và câu tiếp theo */}
                                {(() => {
                                  // Tìm câu hỏi nghe tiếp theo
                                  const listeningQs = questions
                                    .filter(question => question.type === 'listening')
                                    .sort((a, b) => (a.start_at || 0) - (b.start_at || 0));

                                  // Log thông tin chi tiết hơn
                                  if (q.id === 11) {
                                    console.log('Câu 11 - Danh sách các câu hỏi nghe đã sắp xếp:',
                                      listeningQs.map(lq => ({ id: lq.id, start_at: lq.start_at })));
                                  }

                                  const currentIndex = listeningQs.findIndex(lq => lq.id === q.id);

                                  if (currentIndex === -1) return (
                                    <span className="audio-duration-missing">
                                      Không tìm thấy câu hỏi
                                    </span>
                                  );

                                  // Nếu là câu cuối cùng
                                  if (currentIndex === listeningQs.length - 1) {
                                    // Đảm bảo start_at của câu hiện tại được xử lý đúng
                                    // Nếu start_at của câu hiện tại là undefined, sử dụng 0
                                    const currentStartAt = q.start_at !== undefined ? q.start_at : 0;
                                    const duration = listeningDurationSeconds - currentStartAt;
                                    return (
                                      <span className="audio-duration">
                                        Thời lượng: {Math.max(duration, 1)}s (tới cuối file)
                                      </span>
                                    );
                                  }

                                  // Nếu không phải câu cuối cùng
                                  const nextQuestion = listeningQs[currentIndex + 1];
                                  // Đảm bảo start_at của câu hiện tại và câu tiếp theo được xử lý đúng
                                  // Nếu start_at của câu hiện tại là undefined, sử dụng 0
                                  // Nếu start_at của câu tiếp theo là undefined, sử dụng thời lượng file âm thanh
                                  const currentStartAt = q.start_at !== undefined ? q.start_at : 0;
                                  const nextStartAt = nextQuestion.start_at !== undefined ? nextQuestion.start_at : listeningDurationSeconds;
                                  const duration = nextStartAt - currentStartAt;
                                  console.log(`Câu ${q.id}: start_at=${q.start_at}, next start_at=${nextQuestion.start_at}, duration=${duration}`);

                                  return (
                                    <span className="audio-duration">
                                      Thời lượng: {Math.max(duration, 1)}s
                                    </span>
                                  );
                                })()}
                              </div>
                            </div>
                          )}
                        </td>
                        <td className="question-actions">
                          <button
                            type="button"
                            className="icon-button"
                            onClick={() => editQuestion(q.id)}
                          >
                            <FiEdit size={16} />
                            <span className="tooltip">Sửa</span>
                          </button>
                          <button
                            type="button"
                            className="icon-button danger"
                            onClick={() => removeQuestion(q.id)}
                          >
                            <FiTrash2 size={16} />
                            <span className="tooltip">Xóa</span>
                          </button>
                          {index > 0 && (
                            <button
                              type="button"
                              className="icon-button"
                              onClick={() => moveQuestion(index, 'up')}
                            >
                              <FiArrowUp size={16} />
                              <span className="tooltip">Di chuyển lên</span>
                            </button>
                          )}
                          {index < questions.length - 1 && (
                            <button
                              type="button"
                              className="icon-button"
                              onClick={() => moveQuestion(index, 'down')}
                            >
                              <FiArrowDown size={16} />
                              <span className="tooltip">Di chuyển xuống</span>
                            </button>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="form-actions">
          <button type="submit" className="button-with-text primary">
            <FiSave size={18} /> Lưu đề thi
          </button>
        </div>
      </form>

      {/* Form thêm/sửa câu hỏi - hiển thị dạng màn hình riêng */}
      {isEditingQuestion && currentQuestion && (
        <div className="question-editor-overlay">
          <div className="question-editor-modal">
            <div className="question-editor-header">
              <h2>Nội dung câu hỏi</h2>
              <button
                type="button"
                className="icon-button danger"
                onClick={() => {
                  if (window.confirm('Bạn có chắc muốn hủy thay đổi cho câu hỏi này không?')) {
                    setCurrentQuestion(null);
                    setIsEditingQuestion(false);
                  }
                }}
              >
                <FiX size={18} />
                <span className="tooltip">Đóng</span>
              </button>
            </div>

            <div className="question-editor-content">
              <div className="form-row">
                <div className="form-group full-width">
                  <label>Phần thi</label>
                  <select
                    value={currentQuestion.type}
                    onChange={(e) => handleQuestionChange('type', e.target.value as 'reading' | 'listening')}
                  >
                    <option value="reading">Reading</option>
                    <option value="listening">Listening</option>
                  </select>
                </div>
              </div>

              <div className="form-group">
                <label>Câu hỏi</label>
                <textarea
                  value={currentQuestion.text}
                  onChange={(e) => handleQuestionChange('text', e.target.value)}
                  required
                  placeholder="Nhập câu hỏi"
                />
              </div>

              <div className="form-group">
                <label>Văn bản phụ (nếu có)</label>
                <textarea
                  value={currentQuestion.subText}
                  onChange={(e) => handleQuestionChange('subText', e.target.value)}
                  placeholder="Nhập văn bản phụ (nếu có)"
                />
              </div>

              {currentQuestion.type === 'listening' && (
                <>
                  <div className="form-group">
                    <label>Thời điểm bắt đầu (giây)</label>
                    <input
                      type="number"
                      value={currentQuestion.start_at !== undefined ? currentQuestion.start_at : 0}
                      onChange={(e) => handleQuestionChange('start_at', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.1"
                      placeholder="Nhập thời điểm bắt đầu của câu hỏi (giây)"
                      disabled
                      className="disabled-input"
                    />
                    <div className="form-hint">
                      <FiInfo size={14} style={{ marginRight: '4px' }} />
                      Thời điểm bắt đầu được xác định từ công cụ "Ghi nhận thời lượng cho từng câu hỏi"
                    </div>
                  </div>
                  <div className="form-group">
                    <label>Thời lượng câu hỏi (giây)</label>
                    <input
                      type="number"
                      value={currentQuestion.audioDuration || 0}
                      onChange={(e) => handleQuestionChange('audioDuration', parseInt(e.target.value) || 0)}
                      min="0"
                      placeholder="Nhập thời lượng câu hỏi (giây)"
                      disabled
                      className="disabled-input"
                    />
                    <div className="form-hint">
                      <FiInfo size={14} style={{ marginRight: '4px' }} />
                      Thời lượng được tính tự động từ thời điểm bắt đầu của câu hỏi tiếp theo. Chỉ hiển thị, không lưu vào database.
                    </div>
                  </div>
                </>
              )}

              <div className="form-group checkbox-group">
                <label>
                  <input
                    type="checkbox"
                    checked={currentQuestion.hasImageOptions}
                    onChange={(e) => handleQuestionChange('hasImageOptions', e.target.checked)}
                  />
                  Sử dụng hình ảnh cho các đáp án
                </label>
              </div>

              <div className="options-section">
                <h4>Các đáp án</h4>
                {currentQuestion.options.map((option) => (
                  <div key={option.id} className="option-item">
                    <div className="option-header">
                      <div className="option-header-left">
                        <input
                          type="radio"
                          id={`correct-option-${option.id}`}
                          name="correctAnswer"
                          value={option.id}
                          checked={currentQuestion.correctAnswer === option.id}
                          onChange={() => handleQuestionChange('correctAnswer', option.id)}
                        />
                        <label htmlFor={`correct-option-${option.id}`} className="correct-option-label">
                          <strong>Đáp án {option.id}</strong>
                          {currentQuestion.correctAnswer === option.id &&
                            <span className="correct-label"> (Đáp án đúng)</span>
                          }
                        </label>
                      </div>
                    </div>
                    <div className="option-content">
                      {currentQuestion.hasImageOptions ? (
                        <div className="form-group">
                          <label>URL Hình ảnh</label>
                          <input
                            type="text"
                            value={option.imageUrl}
                            onChange={(e) =>
                              handleOptionChange(option.id, 'imageUrl', e.target.value)
                            }
                            placeholder="Nhập URL hình ảnh"
                            required={currentQuestion.hasImageOptions}
                          />
                          {option.imageUrl && (
                            <div className="image-preview">
                              <img src={option.imageUrl} alt={`Preview ${option.id}`} width="100" />
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="form-group">
                          <label>Nội dung</label>
                          <input
                            type="text"
                            value={option.text}
                            onChange={(e) =>
                              handleOptionChange(option.id, 'text', e.target.value)
                            }
                            placeholder="Nhập nội dung đáp án"
                            required={!currentQuestion.hasImageOptions}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="button-group">
                <button
                  type="button"
                  className="button-with-text primary"
                  onClick={saveQuestion}
                >
                  <FiSave size={18} /> Lưu câu hỏi
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showAudioRecorder && (
        <AudioTimelineRecorder
          audioUrl={listeningAudioUrl}
          onStartTimesRecorded={handleStartTimesRecorded}
          onClose={() => setShowAudioRecorder(false)}
          existingStartTimes={existingStartTimes}
        />
      )}
    </div>
  );
};

export default ExamCreator;